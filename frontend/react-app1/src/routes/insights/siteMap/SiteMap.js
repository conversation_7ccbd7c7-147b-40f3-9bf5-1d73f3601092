import React, { useState, useEffect, useCallback } from 'react';
import { Tabs, Spin } from 'antd';
import GoogleMapComponent from '../../../components/wify-utils/GoogleMapComponent';
import { getCenterLocFrIndMap } from '../../../util/helpers';
import http_utils from '../../../util/http_utils';

const { TabPane } = Tabs;

// Helper function for API parameters
const getRandomuserParams = (params) => ({
    results: params.pagination?.pageSize || 10,
    page: params.pagination?.page || 1,
    ...params,
});

const SiteMapOverview = ({ filterObject, searchQuery }) => {
    const [loading, setLoading] = useState(false);
    const [data, setData] = useState([]);
    const [apiResp, setApiResp] = useState({ data: [] });

    // Mock data for sites
    const mockSiteData = [
        {
            site_id: 'SITE001',
            pincode: '110001',
            geocoding_location_data: {
                location: {
                    lat: 28.6139,
                    lng: 77.209,
                },
            },
            site_name: 'Delhi Central Site',
            address: 'Connaught Place, New Delhi',
        },
        {
            site_id: 'SITE002',
            pincode: '400001',
            geocoding_location_data: {
                location: {
                    lat: 19.076,
                    lng: 72.8777,
                },
            },
            site_name: 'Mumbai Fort Site',
            address: 'Fort, Mumbai',
        },
        {
            site_id: 'SITE003',
            pincode: '560001',
            geocoding_location_data: {
                location: {
                    lat: 12.9716,
                    lng: 77.5946,
                },
            },
            site_name: 'Bangalore Central Site',
            address: 'MG Road, Bangalore',
        },
        {
            site_id: 'SITE004',
            pincode: '600001',
            geocoding_location_data: {
                location: {
                    lat: 13.0827,
                    lng: 80.2707,
                },
            },
            site_name: 'Chennai Central Site',
            address: 'George Town, Chennai',
        },
        {
            site_id: 'SITE005',
            pincode: '700001',
            geocoding_location_data: {
                location: {
                    lat: 22.5726,
                    lng: 88.3639,
                },
            },
            site_name: 'Kolkata Central Site',
            address: 'BBD Bagh, Kolkata',
        },
        {
            site_id: 'SITE006',
            pincode: '500001',
            geocoding_location_data: {
                location: {
                    lat: 17.385,
                    lng: 78.4867,
                },
            },
            site_name: 'Hyderabad Central Site',
            address: 'Abids, Hyderabad',
        },
    ];

    useEffect(() => {
        fetchSiteData();
    }, [fetchSiteData]);

    const fetchSiteData = useCallback(() => {
        const params = {
            filters: filterObject,
            search_query: searchQuery || '',
        };

        setLoading(true);

        const onSuccess = (response) => {
            console.log(
                'SiteMapOverview :: fetchSiteData :: success : ',
                response
            );
            setData(response.data.data);
            setApiResp(response.data);
            setLoading(false);
        };

        const onError = (error) => {
            console.error(
                'SiteMapOverview :: fetchSiteData :: error : ',
                error
            );
            // Fallback to mock data on error
            let filteredData = mockSiteData;

            // Apply search filter to mock data
            if (searchQuery) {
                filteredData = mockSiteData.filter(
                    (site) =>
                        site.site_id
                            .toLowerCase()
                            .includes(searchQuery.toLowerCase()) ||
                        site.pincode.includes(searchQuery) ||
                        site.site_name
                            .toLowerCase()
                            .includes(searchQuery.toLowerCase())
                );
            }

            setData(filteredData);
            setApiResp({ data: filteredData });
            setLoading(false);
        };

        // Make API call to site-map endpoint
        http_utils.performGetCall(
            '/site-map',
            getRandomuserParams(params),
            onSuccess,
            onError
        );
    }, [filterObject, searchQuery, mockSiteData]);

    const getCustomMarkerOptions = (location) => {
        return {
            title: `Pincode: ${location.pincode}`,
            icon: {
                url: 'https://maps.google.com/mapfiles/ms/icons/red-dot.png',
                scaledSize: new window.google.maps.Size(32, 32),
            },
        };
    };

    const getInfoWindowContent = (location) => {
        return `
            <div style="padding: 10px; min-width: 200px;">
                <h4 style="margin: 0 0 8px 0; color: #1890ff;">
                    Pincode: ${location.pincode}
                </h4>
                <p style="margin: 4px 0; font-weight: bold;">
                    Site ID: ${location.site_id}
                </p>
                <p style="margin: 4px 0;">
                    <strong>Site Name:</strong> ${location.site_name}
                </p>
                <p style="margin: 4px 0;">
                    <strong>Address:</strong> ${location.address}
                </p>
                <p style="margin: 4px 0; font-size: 12px; color: #666;">
                    Lat: ${location.geocoding_location_data.location.lat}, 
                    Lng: ${location.geocoding_location_data.location.lng}
                </p>
            </div>
        `;
    };

    if (loading) {
        return (
            <div
                className="gx-d-flex gx-justify-content-center gx-align-items-center"
                style={{ height: '400px' }}
            >
                <Spin size="large" />
            </div>
        );
    }

    return (
        <div className="gx-p-1">
            <Tabs defaultActiveKey="marker" className="gx-text-capitalize">
                <TabPane
                    key="marker"
                    style={{ height: 'calc(100vh - 250px)' }}
                    tab={
                        <span>
                            <i className="icon icon-map-street-view"></i> MARKER
                        </span>
                    }
                >
                    <GoogleMapComponent
                        center={getCenterLocFrIndMap()}
                        zoom={5}
                        markerMapMode={true}
                        locations={apiResp?.data}
                        customMarkerOptionsCallback={getCustomMarkerOptions}
                        infoWindowContentCallback={getInfoWindowContent}
                    />
                </TabPane>

                <TabPane
                    key="heat_map"
                    style={{ height: 'calc(100vh - 250px)' }}
                    tab={
                        <span>
                            <i className="icon icon-map-clustering gx-mr-1"></i>{' '}
                            HEAT MAP
                        </span>
                    }
                >
                    <GoogleMapComponent
                        center={getCenterLocFrIndMap()}
                        zoom={5}
                        locations={apiResp?.data}
                        customMarkerOptionsCallback={getCustomMarkerOptions}
                        infoWindowContentCallback={getInfoWindowContent}
                    />
                </TabPane>
            </Tabs>

            {/* Display data count */}
            <div className="gx-mt-2 gx-text-center gx-text-grey">
                Showing {data.length} sites
            </div>
        </div>
    );
};

export default SiteMapOverview;
