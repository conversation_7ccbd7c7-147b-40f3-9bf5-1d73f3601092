import React, { useState, useEffect } from 'react';
import { Drawer } from 'antd';
import { useSelector } from 'react-redux';
import CustomScrollbars from '../../../util/CustomScrollbars';
import SiteMapOverview from './SiteMap';
import AppModuleHeader from '../../../components/AppModuleHeader';
import {
    NAV_STYLE_DRAWER,
    NAV_STYLE_FIXED,
    NAV_STYLE_MINI_SIDEBAR,
    TAB_SIZE,
    THEME_TYPE_LITE,
} from '../../../constants/ThemeSetting';

const SiteMap = () => {
    const [searchFilter, setSearchFilter] = useState('');
    const [drawerState, setDrawerState] = useState(false);
    const [activeFilters, setActiveFilters] = useState({});

    const { navStyle, themeType, width } = useSelector(({ settings }) => settings);

    const getNavStyleSubMenuClass = (navStyle) => {
        if (navStyle === NAV_STYLE_FIXED || navStyle === NAV_STYLE_MINI_SIDEBAR) {
            return 'gx-module-content-scroll';
        } else {
            return '';
        }
    };

    const onToggleDrawer = () => {
        setDrawerState(!drawerState);
    };

    const handleSearchChange = (value) => {
        setSearchFilter(value);
    };

    const getExportMenuData = () => {
        return [
            {
                key: 'export-csv',
                label: 'Export CSV',
                icon: 'icon-export',
                onClick: () => {
                    console.log('Export CSV clicked');
                    // TODO: Implement CSV export functionality
                },
            },
            {
                key: 'export-pdf',
                label: 'Export PDF',
                icon: 'icon-pdf',
                onClick: () => {
                    console.log('Export PDF clicked');
                    // TODO: Implement PDF export functionality
                },
            },
        ];
    };

    return (
        <>
            {navStyle === NAV_STYLE_DRAWER || width < TAB_SIZE ? (
                <Drawer
                    placement="left"
                    closable={false}
                    visible={drawerState}
                    onClose={onToggleDrawer}
                    className="gx-module-side-content"
                >
                    <div className="gx-module-side-header">
                        <div className="gx-module-side-header-content">
                            <i
                                className="icon icon-filter gx-icon-btn"
                                aria-label="Menu"
                                onClick={onToggleDrawer}
                            />
                        </div>
                    </div>
                    <div className="gx-module-side-content">
                        <CustomScrollbars className="gx-module-side-scroll">
                            <div className="gx-module-side-content-inner">
                                {/* TODO: Add filter components here */}
                                <div className="gx-p-4">
                                    <h4>Filters</h4>
                                    <p>Filter options will be added here</p>
                                </div>
                            </div>
                        </CustomScrollbars>
                    </div>
                </Drawer>
            ) : null}

            {navStyle !== NAV_STYLE_DRAWER && width >= TAB_SIZE && (
                <div className="gx-module-side">
                    <div className="gx-module-side-header">
                        <div className="gx-module-side-header-content">
                            <h2 className="gx-module-side-title">Site Map Filters</h2>
                        </div>
                    </div>
                    <div className="gx-module-side-content">
                        <CustomScrollbars className="gx-module-side-scroll">
                            <div className="gx-module-side-content-inner">
                                {/* TODO: Add filter components here */}
                                <div className="gx-p-4">
                                    <h4>Filters</h4>
                                    <p>Filter options will be added here</p>
                                </div>
                            </div>
                        </CustomScrollbars>
                    </div>
                </div>
            )}

            {(navStyle === NAV_STYLE_DRAWER || width < TAB_SIZE) && (
                <div className="gx-module-content">
                    <div className="gx-module-box">
                        <div className="gx-module-box-header">
                            <span className="gx-drawer-btn gx-d-flex gx-d-lg-none">
                                <i
                                    className="icon icon-filter gx-icon-btn"
                                    aria-label="Menu"
                                    onClick={onToggleDrawer}
                                />
                            </span>

                            <AppModuleHeader
                                placeholder="Search by Site ID, Pincode..."
                                currValue={searchFilter}
                                optionsMenuData={getExportMenuData()}
                                onChange={handleSearchChange}
                            />
                        </div>
                        <div className="gx-module-box-content gx-px-1 gx-py-2">
                            <CustomScrollbars>
                                <SiteMapOverview
                                    filterObject={activeFilters}
                                    searchQuery={searchFilter}
                                />
                            </CustomScrollbars>
                        </div>
                    </div>
                </div>
            )}

            {navStyle !== NAV_STYLE_DRAWER && width >= TAB_SIZE && (
                <div className={`gx-module-content ${getNavStyleSubMenuClass(navStyle)}`}>
                    <div className="gx-module-box">
                        <div className="gx-module-box-header">
                            <span className="gx-drawer-btn gx-d-flex gx-d-lg-none">
                                <i
                                    className="icon icon-filter gx-icon-btn"
                                    aria-label="Menu"
                                    onClick={onToggleDrawer}
                                />
                            </span>

                            <AppModuleHeader
                                placeholder="Search by Site ID, Pincode..."
                                currValue={searchFilter}
                                optionsMenuData={getExportMenuData()}
                                onChange={handleSearchChange}
                            />
                        </div>
                        <div className="gx-module-box-content gx-px-1 gx-py-2">
                            <CustomScrollbars>
                                <SiteMapOverview
                                    filterObject={activeFilters}
                                    searchQuery={searchFilter}
                                />
                            </CustomScrollbars>
                        </div>
                    </div>
                </div>
            )}
        </>
    );
};

export default SiteMap;
